/**
 * Email service main module
 * Provides a simple interface for sending emails with multiple provider support
 */

import type { EmailOptions, EmailResult, EmailService } from './types';

import logger from '../logger';
import { EmailServiceRegistry } from './factory';

// Global email service registry
const emailRegistry = new EmailServiceRegistry();

/**
 * Initialize email services based on environment configuration
 */
function initializeEmailServices(): void {
  try {
    // TODO: Add email service configuration to env.ts
    // For now, we'll use a basic nodemailer configuration
    // This should be moved to environment configuration

    const nodemailerConfig = {
      provider: 'nodemailer' as const,
      config: {
        host: process.env.SMTP_HOST || 'localhost',
        port: parseInt(process.env.SMTP_PORT || '587', 10),
        secure: process.env.SMTP_SECURE === 'true',
        auth:
          process.env.SMTP_USER && process.env.SMTP_PASS
            ? {
                user: process.env.SMTP_USER,
                pass: process.env.SMTP_PASS,
              }
            : undefined,
      },
    };

    emailRegistry.registerFromConfig('default', nodemailerConfig);
    logger.info('Email service initialized with Nodemailer provider');
  } catch (error) {
    logger.error({ error: (error as Error).message }, 'Failed to initialize email services');
    throw error;
  }
}

/**
 * Get the default email service
 */
export function getEmailService(name?: string): EmailService {
  // Initialize services if not already done
  if (emailRegistry.getServiceNames().length === 0) {
    initializeEmailServices();
  }

  return emailRegistry.get(name);
}

/**
 * Send an email using the default service
 */
export async function sendEmail(options: EmailOptions): Promise<EmailResult> {
  const service = getEmailService();

  logger.info(
    {
      to: Array.isArray(options.to) ? options.to.length : 1,
      subject: options.subject,
      provider: service.getProvider(),
    },
    'Sending email'
  );

  const result = await service.send(options);

  if (result.success) {
    logger.info({ messageId: result.messageId, provider: result.provider }, 'Email sent successfully');
  } else {
    logger.error({ error: result.error, provider: result.provider }, 'Failed to send email');
  }

  return result;
}

/**
 * Test email service connection
 */
export async function testEmailService(name?: string): Promise<boolean> {
  try {
    const service = getEmailService(name);

    if (service.test) {
      const isConnected = await service.test();
      logger.info(
        { provider: service.getProvider() },
        `Email service test result: ${isConnected ? 'success' : 'failed'}`
      );
      return isConnected;
    }

    logger.warn({ provider: service.getProvider() }, 'Email service does not support connection testing');
    return false;
  } catch (error) {
    logger.error({ error: (error as Error).message }, 'Email service test failed');
    return false;
  }
}

/**
 * Get email service registry for advanced usage
 */
export function getEmailRegistry(): EmailServiceRegistry {
  return emailRegistry;
}

// Export types and classes for external use
export * from './types';
export * from './factory';
