import type { Server } from 'node:http';

import { afterAll, beforeAll, describe, expect, test } from 'bun:test';

import { createTestApp, getTestConfig } from '../helpers/app';

describe('Express App Integration Tests', () => {
  let app: ReturnType<typeof createTestApp>;
  let server: Server;
  let config: ReturnType<typeof getTestConfig>;
  let baseUrl: string;

  beforeAll(async () => {
    app = createTestApp();
    config = getTestConfig();

    // Start server on a random available port for testing
    const testPort = config.port + 1000; // Use a different port for tests
    baseUrl = `http://localhost:${testPort}`;

    return new Promise<void>((resolve) => {
      server = app.listen(testPort, () => {
        resolve();
      });
    });
  });

  afterAll(async () => {
    if (server) {
      return new Promise<void>((resolve) => {
        server.close(() => {
          resolve();
        });
      });
    }
  });

  describe('Public endpoints', () => {
    test('GET / should return service status', async () => {
      const res = await fetch(`${baseUrl}/`);
      const data = await res.json();

      expect(res.status).toBe(200);
      expect(data).toEqual({
        message: 'OK',
        success: true,
        app: 'Finanze.Pro Emails Service',
      });
    });

    test('GET /health should return health status', async () => {
      const res = await fetch(`${baseUrl}/health`);
      const data = await res.json();

      expect(res.status).toBe(200);
      expect(data).toEqual({
        message: 'Healthy',
        success: true,
      });
    });
  });

  describe('Protected endpoints - Authentication required', () => {
    test('POST /emails/v1/password-reset without auth should return 401', async () => {
      const res = await fetch(`${baseUrl}/emails/v1/password-reset`, {
        method: 'POST',
      });
      const data = await res.json();

      expect(res.status).toBe(401);
      expect(data).toEqual({
        success: false,
        error: 'Authentication required',
        message: 'API key must be provided in Authorization header (Bearer token) or x-api-key header',
      });
    });

    test('POST /emails/v1/password-reset with invalid Bearer token should return 401', async () => {
      const res = await fetch(`${baseUrl}/emails/v1/password-reset`, {
        method: 'POST',
        headers: {
          Authorization: 'Bearer invalid-key',
        },
      });
      const data = await res.json();

      expect(res.status).toBe(401);
      expect(data).toEqual({
        success: false,
        error: 'Authentication failed',
        message: 'Invalid API key',
      });
    });

    test('POST /emails/v1/password-reset with invalid x-api-key should return 401', async () => {
      const res = await fetch(`${baseUrl}/emails/v1/password-reset`, {
        method: 'POST',
        headers: {
          'x-api-key': 'invalid-key',
        },
      });
      const data = await res.json();

      expect(res.status).toBe(401);
      expect(data).toEqual({
        success: false,
        error: 'Authentication failed',
        message: 'Invalid API key',
      });
    });
  });

  describe('Protected endpoints - Valid authentication', () => {
    test('POST /emails/v1/password-reset with valid Bearer token should succeed', async () => {
      const res = await fetch(`${baseUrl}/emails/v1/password-reset`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${config.apiKey}`,
        },
      });
      const data = await res.json();

      expect(res.status).toBe(200);
      expect(data).toEqual({
        success: true,
        message: 'Email sent',
      });
    });

    test('POST /emails/v1/password-reset with valid x-api-key should succeed', async () => {
      const res = await fetch(`${baseUrl}/emails/v1/password-reset`, {
        method: 'POST',
        headers: {
          'x-api-key': config.apiKey,
        },
      });
      const data = await res.json();

      expect(res.status).toBe(200);
      expect(data).toEqual({
        success: true,
        message: 'Email sent',
      });
    });
  });

  describe('Authentication header precedence', () => {
    test('should prioritize Authorization header when both headers are present', async () => {
      const res = await fetch(`${baseUrl}/emails/v1/password-reset`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${config.apiKey}`,
          'x-api-key': 'different-invalid-key',
        },
      });
      const data = await res.json();

      expect(res.status).toBe(200);
      expect(data).toEqual({
        success: true,
        message: 'Email sent',
      });
    });

    test('should fall back to x-api-key when Authorization header is malformed', async () => {
      const res = await fetch(`${baseUrl}/emails/v1/password-reset`, {
        method: 'POST',
        headers: {
          Authorization: 'InvalidFormat wrong-key',
          'x-api-key': config.apiKey,
        },
      });
      const data = await res.json();

      expect(res.status).toBe(200);
      expect(data).toEqual({
        success: true,
        message: 'Email sent',
      });
    });
  });

  describe('Edge cases', () => {
    test('should handle empty Authorization header', async () => {
      const res = await fetch(`${baseUrl}/emails/v1/password-reset`, {
        method: 'POST',
        headers: {
          Authorization: '',
        },
      });
      const data = await res.json();

      expect(res.status).toBe(401);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Authentication required');
    });

    test('should handle empty x-api-key header', async () => {
      const res = await fetch(`${baseUrl}/emails/v1/password-reset`, {
        method: 'POST',
        headers: {
          'x-api-key': '',
        },
      });
      const data = await res.json();

      expect(res.status).toBe(401);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Authentication required');
    });

    test('should handle Bearer token without space', async () => {
      const res = await fetch(`${baseUrl}/emails/v1/password-reset`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer${config.apiKey}`, // Missing space
        },
      });
      const data = await res.json();

      expect(res.status).toBe(401);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Authentication required');
    });

    test('should handle case-sensitive header names', async () => {
      const res = await fetch(`${baseUrl}/emails/v1/password-reset`, {
        method: 'POST',
        headers: {
          'X-API-KEY': config.apiKey, // Different case
        },
      });
      const data = await res.json();

      // Should succeed because Express headers are case-insensitive
      expect(res.status).toBe(200);
      expect(data.success).toBe(true);
    });
  });
});
