import express from 'express';
import { pinoHttp } from 'pino-http';

import { apiKeyAuth } from '~/lib/auth';
import env from '~/lib/env';
import logger from '~/lib/logger';

import v1Router from '../../controllers/v1';

/**
 * Create Express app for testing
 * This mirrors the main app setup but without starting the server
 */
export function createTestApp() {
  const app = express();

  app.use(pinoHttp({ logger }));
  app.use(express.static('public'));

  // Public endpoints
  app.get('/', (_req, res) => {
    res.json({ message: 'OK', success: true, app: 'Finanze.Pro Emails Service' });
  });

  app.get('/health', (_req, res) => {
    res.json({ message: 'Healthy', success: true });
  });

  // Protected endpoints
  app.use('/emails/v1', apiKeyAuth, v1Router);

  return app;
}

/**
 * Get test environment configuration
 */
export function getTestConfig() {
  return {
    apiKey: env.API_KEY,
    port: env.PORT,
    nodeEnv: env.NODE_ENV,
  };
}
