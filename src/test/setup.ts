/**
 * Test setup file
 * This file is loaded before all tests run
 */

// Ensure we're in test environment
process.env.NODE_ENV = 'test';

// Set test-specific environment variables if not already set
if (!process.env.API_KEY) {
  process.env.API_KEY = 'test-api-key-12345';
}

if (!process.env.LOG_LEVEL) {
  process.env.LOG_LEVEL = 'silent';
}

if (!process.env.PORT) {
  process.env.PORT = '3002';
}
