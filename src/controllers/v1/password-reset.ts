import type { Request, Response } from 'express';

import { z } from 'zod';

import { sendEmail } from '~/lib/email';
import logger from '~/lib/logger';

// Validation schema for password reset request
const PasswordResetSchema = z.object({
  email: z.email('Invalid email address'),
  resetUrl: z.url('Invalid reset URL').optional(),
});

const passwordReset = async (req: Request, res: Response) => {
  try {
    // Validate request body
    const validationResult = PasswordResetSchema.safeParse(req.body);

    if (!validationResult.success) {
      logger.warn(
        {
          errors: z.treeifyError(validationResult.error),
          body: req.body as unknown as object,
        },
        'Invalid password reset request'
      );

      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        message: 'Invalid email address provided',
        details: z.treeifyError(validationResult.error),
      });
    }

    const { email, resetUrl } = validationResult.data;
    const defaultResetUrl = resetUrl || `${process.env.FRONTEND_URL || 'https://app.finanze.pro'}/reset-password`;

    // Send password reset email
    const result = await sendEmail({
      to: email,
      from: process.env.DEFAULT_FROM_EMAIL || '<EMAIL>',
      subject: 'Password Reset Request - Finanze.Pro',
      text: `You have requested a password reset. Please visit the following link to reset your password: ${defaultResetUrl}`,
      html: `
        <h2>Password Reset Request</h2>
        <p>You have requested a password reset for your Finanze.Pro account.</p>
        <p>Please click the link below to reset your password:</p>
        <p><a href="${defaultResetUrl}" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Reset Password</a></p>
        <p>If you did not request this password reset, please ignore this email.</p>
        <p>Best regards,<br>The Finanze.Pro Team</p>
      `,
    });

    if (result.success) {
      return res.status(200).json({
        success: true,
        message: 'Password reset email sent successfully',
        messageId: result.messageId,
      });
    } else {
      return res.status(500).json({
        success: false,
        error: 'Email sending failed',
        message: result.message,
      });
    }
  } catch (error) {
    logger.error({ error: (error as Error).message }, 'Unexpected error in password reset controller');

    return res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: 'An unexpected error occurred while sending password reset email',
    });
  }
};

export default passwordReset;
