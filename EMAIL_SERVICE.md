# Email Service

A flexible, provider-agnostic email service for the Finanze.Pro Emails microservice. This service provides a unified interface for sending emails through multiple providers like Nodemailer and Resend.

## Features

- **Provider Agnostic**: Support for multiple email providers with a unified interface
- **Type Safe**: Full TypeScript support with comprehensive type definitions
- **Flexible Configuration**: Easy switching between providers and configurations
- **Service Registry**: Manage multiple email service instances
- **Rich Email Options**: Support for HTML/text content, attachments, CC/BCC, custom headers
- **Error Handling**: Comprehensive error reporting with provider information
- **Logging**: Integrated logging with Pino
- **Testing Support**: Built-in connection testing capabilities

## Supported Providers

- ✅ **Nodemailer** - Full support for SMTP and various transports
- 🚧 **Resend** - Placeholder implementation (ready for integration)

## Quick Start

### Basic Usage

```typescript
import { sendEmail } from '~/lib/email';

// Send a simple email
const result = await sendEmail({
  to: '<EMAIL>',
  from: '<EMAIL>',
  subject: 'Welcome!',
  text: 'Thank you for signing up!',
  html: '<h1>Welcome!</h1><p>Thank you for signing up!</p>',
});

console.log(result);
// {
//   success: true,
//   messageId: "abc123",
//   message: "Email sent successfully",
//   provider: "nodemailer",
//   timestamp: "2024-01-01T00:00:00.000Z"
// }
```

### Advanced Usage

```typescript
import { EmailServiceFactory, EmailServiceRegistry } from '~/lib/email';

// Create a service registry
const registry = new EmailServiceRegistry();

// Register multiple providers
registry.registerFromConfig('transactional', {
  provider: 'nodemailer',
  config: {
    host: 'smtp.gmail.com',
    port: 587,
    secure: false,
    auth: {
      user: '<EMAIL>',
      pass: 'your-app-password',
    },
  },
});

registry.registerFromConfig('marketing', {
  provider: 'resend',
  config: {
    apiKey: 'your-resend-api-key',
  },
});

// Use specific provider
const transactionalService = registry.get('transactional');
const result = await transactionalService.send({
  to: '<EMAIL>',
  subject: 'Order Confirmation',
  html: '<h1>Your order has been confirmed!</h1>',
});
```

## API Reference

### EmailOptions Interface

```typescript
interface EmailOptions {
  to: EmailAddress | EmailAddress[] | string | string[];
  from: EmailAddress | string;
  subject: string;
  text?: string;
  html?: string;
  cc?: EmailAddress | EmailAddress[] | string | string[];
  bcc?: EmailAddress | EmailAddress[] | string | string[];
  replyTo?: EmailAddress | string;
  attachments?: EmailAttachment[];
  headers?: Record<string, string>;
}
```

### EmailResult Interface

```typescript
interface EmailResult {
  success: boolean;
  messageId?: string;
  message: string;
  error?: string;
  provider: string;
  timestamp: Date;
}
```

### EmailService Interface

```typescript
interface EmailService {
  send(options: EmailOptions): Promise<EmailResult>;
  getProvider(): string;
  test?(): Promise<boolean>;
}
```

## Configuration

### Environment Variables

The service uses the following environment variables for default configuration:

```bash
# SMTP Configuration (for Nodemailer)
SMTP_HOST=localhost
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=your-username
SMTP_PASS=your-password

# Default email addresses
DEFAULT_FROM_EMAIL=<EMAIL>
FRONTEND_URL=https://yourapp.com
```

### Nodemailer Configuration

```typescript
const nodemailerConfig = {
  provider: 'nodemailer' as const,
  config: {
    host: 'smtp.gmail.com',
    port: 587,
    secure: false, // true for 465, false for other ports
    auth: {
      user: '<EMAIL>',
      pass: 'your-app-password',
    },
    tls: {
      rejectUnauthorized: false, // Optional
    },
  },
};
```

### Resend Configuration (Future)

```typescript
const resendConfig = {
  provider: 'resend' as const,
  config: {
    apiKey: 'your-resend-api-key',
    baseUrl: 'https://api.resend.com', // Optional
  },
};
```

## HTTP Endpoints

### POST /emails/v1/password-reset

Send a password reset email (specialized endpoint).

**Request Body:**

```json
{
  "email": "<EMAIL>",
  "resetUrl": "https://yourapp.com/reset-password?token=abc123"
}
```

## Error Handling

The service provides detailed error information:

```typescript
{
  success: false,
  message: "Failed to send email",
  error: "SMTP connection failed",
  provider: "nodemailer",
  timestamp: "2024-01-01T00:00:00.000Z"
}
```

## Testing

Run the email service tests:

```bash
bun test src/test/lib/email.test.ts
```

Test connection to email provider:

```typescript
import { testEmailService } from '~/lib/email';

const isConnected = await testEmailService();
console.log('Email service connected:', isConnected);
```

## Examples

See `examples/email-service-usage.ts` for comprehensive usage examples including:

- Simple email sending
- Complex emails with attachments
- Multiple provider setup
- Custom service instances
- Error handling and retry logic

## Adding New Providers

To add a new email provider:

1. Create a new provider class in `src/lib/email/providers/`
2. Implement the `EmailService` interface
3. Add the provider to the `EmailServiceFactory`
4. Update the `EmailServiceConfig` type
5. Add tests for the new provider

Example provider structure:

```typescript
export class NewProviderEmailService implements EmailService {
  constructor(config: NewProviderConfig) {
    // Initialize provider
  }

  async send(options: EmailOptions): Promise<EmailResult> {
    // Implement email sending
  }

  getProvider(): string {
    return 'new-provider';
  }

  async test(): Promise<boolean> {
    // Test connection
  }
}
```
